export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL
export const WEBSOCKET_URL = import.meta.env.VITE_WEBSOCKET_URL

export const API_ENDPOINTS = {
  auth: {
    login: '/auth/login',
    signup: '/auth/signup',
    refresh: '/auth/refresh',
    logout: '/auth/logout',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    socialAuth: '/auth/social',
  },
  files: {
    initUpload: '/files/upload/init',
    validate: (fileId: string) => `/files/validate/${fileId}`,
    list: '/files',
    delete: (fileId: string) => `/files/${fileId}`,
    download: (fileId: string) => `/files/${fileId}/download`,
  },
  analysis: {
    start: '/analysis/start',
    status: (analysisId: string) => `/analysis/status/${analysisId}`,
    results: (analysisId: string) => `/analysis/results/${analysisId}`,
    download: (analysisId: string) => `/analysis/results/${analysisId}/download`,
    configure: '/analysis/configure',
  },
  user: {
    profile: '/user/profile',
    updateProfile: '/user/profile',
    changePassword: '/user/change-password',
    preferences: '/user/preferences',
  },
}

export const WEBSOCKET_EVENTS = {
  connect: '$connect',
  disconnect: '$disconnect',
  analysisProgress: 'analysis:progress',
  analysisComplete: 'analysis:complete',
  analysisError: 'analysis:error',
  fileValidated: 'file:validated',
  fileError: 'file:error',
}

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
}

export const API_CONFIG = {
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
}

export const FILE_UPLOAD_CONFIG = {
  maxSize: 500 * 1024 * 1024, // 500MB
  allowedTypes: ['.edf'],
  chunkSize: 5 * 1024 * 1024, // 5MB chunks for multipart
}

export const OAUTH_CONFIG = {
  google: {
    clientId: import.meta.env.VITE_GOOGLE_CLIENT_ID || '',
    redirectUri: `${window.location.origin}/auth/google/callback`,
    scope: 'openid email profile',
  },
  github: {
    clientId: import.meta.env.VITE_GITHUB_CLIENT_ID || '',
    redirectUri: `${window.location.origin}/auth/github/callback`,
    scope: 'read:user user:email',
  },
}