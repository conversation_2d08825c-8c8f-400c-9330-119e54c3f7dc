import { useCallback, useState } from 'react'
import { useAppDispatch, useAppSelector } from '../store'
import {
  addFiles,
  removeFile,
  updateFileStatus,
  updateFileProgress,
  setIsUploading,
  clearAllFiles,
} from '../store/slices/filesSlice'
import { filesService } from '../services/files.service'
import type { FileUpload } from '../types/files'
import { showToast } from '../utils/toast'

interface UseFileUploadOptions {
  onSuccess?: (fileId: string) => void
  onError?: (error: Error, fileId: string) => void
  maxConcurrentUploads?: number
}

export function useFileUpload(options: UseFileUploadOptions = {}) {
  const dispatch = useAppDispatch()
  const { files, isUploading } = useAppSelector((state) => state.files)
  const [uploadQueue, setUploadQueue] = useState<Set<string>>(new Set())
  const { maxConcurrentUploads = 3 } = options

  const validateFiles = useCallback((fileList: File[]): File[] => {
    const validFiles: File[] = []

    fileList.forEach((file) => {
      const error = filesService.getFileValidationError(file)
      if (error) {
        showToast.error(`${file.name}: ${error}`)
      } else {
        validFiles.push(file)
      }
    })

    return validFiles
  }, [])

  const addFilesToQueue = useCallback(
    (fileList: File[]) => {
      const validFiles = validateFiles(fileList)

      if (validFiles.length === 0) return

      dispatch(addFiles({ files: validFiles }))

      if (!isUploading) {
        processUploadQueue()
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch, isUploading, validateFiles]
  )

  const uploadFile = useCallback(
    async (file: FileUpload) => {
      if (!file.file) return

      dispatch(updateFileStatus({ id: file.id, status: 'uploading' }))
      setUploadQueue((prev) => new Set(prev).add(file.id))

      try {
        const result = await filesService.uploadFile(file.file, (progress) => {
          dispatch(
            updateFileProgress({
              id: file.id,
              progress: progress.progress,
            })
          )
        })

        dispatch(
          updateFileStatus({
            id: file.id,
            status: result.status === 'valid' ? 'valid' : 'error',
            errorMessage: result.status !== 'valid' ? 'File validation failed' : undefined,
          })
        )

        if (result.status === 'valid' && options.onSuccess) {
          options.onSuccess(result.fileId)
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed'
        dispatch(
          updateFileStatus({
            id: file.id,
            status: 'error',
            errorMessage,
          })
        )

        if (options.onError) {
          options.onError(error instanceof Error ? error : new Error(String(error)), file.id)
        }
      } finally {
        setUploadQueue((prev) => {
          const newSet = new Set(prev)
          newSet.delete(file.id)
          return newSet
        })
      }
    },
    [dispatch, options]
  )

  const processUploadQueue = useCallback(async () => {
    const pendingFiles = files.filter((f) => f.status === 'pending')
    const currentlyUploading = uploadQueue.size

    if (pendingFiles.length === 0 || currentlyUploading >= maxConcurrentUploads) {
      if (pendingFiles.length === 0 && currentlyUploading === 0) {
        dispatch(setIsUploading(false))
      }
      return
    }

    dispatch(setIsUploading(true))

    const filesToUpload = pendingFiles.slice(0, maxConcurrentUploads - currentlyUploading)

    await Promise.all(filesToUpload.map((file) => uploadFile(file)))

    processUploadQueue()
  }, [files, uploadQueue, maxConcurrentUploads, dispatch, uploadFile])

  const cancelUpload = useCallback(
    (fileId: string) => {
      filesService.cancelUpload(fileId)
      dispatch(removeFile(fileId))
      setUploadQueue((prev) => {
        const newSet = new Set(prev)
        newSet.delete(fileId)
        return newSet
      })
    },
    [dispatch]
  )

  const retryUpload = useCallback(
    (fileId: string) => {
      const file = files.find((f) => f.id === fileId)
      if (!file) return

      dispatch(updateFileStatus({ id: fileId, status: 'pending' }))
      dispatch(updateFileProgress({ id: fileId, progress: 0 }))

      if (!isUploading) {
        processUploadQueue()
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [files, dispatch, isUploading]
  )

  const clearFiles = useCallback(() => {
    uploadQueue.forEach((fileId) => {
      filesService.cancelUpload(fileId)
    })
    dispatch(clearAllFiles())
    setUploadQueue(new Set())
  }, [dispatch, uploadQueue])

  const deleteFile = useCallback(
    async (fileId: string, serverFileId?: string) => {
      try {
        if (serverFileId) {
          await filesService.deleteFile(serverFileId)
        }
        dispatch(removeFile(fileId))
        showToast.success('File deleted successfully')
      } catch {
        showToast.error('Failed to delete file')
      }
    },
    [dispatch]
  )

  return {
    files,
    isUploading,
    uploadingCount: uploadQueue.size,
    addFiles: addFilesToQueue,
    cancelUpload,
    retryUpload,
    clearFiles,
    deleteFile,
    processQueue: processUploadQueue,
  }
}

export function useFileList() {
  const [files, setFiles] = useState<
    Array<{ id: string; name: string; size: number; uploadedAt: string; status: string }>
  >([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [total, setTotal] = useState(0)

  const fetchFiles = useCallback(async (pageNum = 1) => {
    setIsLoading(true)
    setError(null)

    try {
      const result = await filesService.listFiles(pageNum)
      setFiles(result.files)
      setTotal(result.total)
      setPage(pageNum)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch files')
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    files,
    isLoading,
    error,
    page,
    total,
    fetchFiles,
    refresh: () => fetchFiles(page),
  }
}
