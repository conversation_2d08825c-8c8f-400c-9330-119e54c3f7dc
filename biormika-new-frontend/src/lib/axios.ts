import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig } from 'axios'
import { API_BASE_URL, API_CONFIG, API_ENDPOINTS } from '../constants/api'
import type { ApiAxiosError, ApiAxiosResponse, RequestConfig } from '../types/axios'
import { showToast } from '../utils/toast'
import { handleApiError } from '../utils/errorHandler'
import { store } from '../store'
import { logout, updateToken } from '../store/slices/authSlice'

interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean
  _retryCount?: number
  _requestConfig?: RequestConfig
}

let isRefreshing = false
let failedQueue: Array<{
  resolve: (value: string | null) => void
  reject: (reason: Error) => void
}> = []

const processQueue = (error: Error | null, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error)
    } else {
      prom.resolve(token)
    }
  })
  
  failedQueue = []
}

const axiosInstance: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
  retry: API_CONFIG.retries,
  retryDelay: API_CONFIG.retryDelay,
})

axiosInstance.interceptors.request.use(
  (config: ExtendedAxiosRequestConfig) => {
    const state = store.getState()
    const token = state.auth.token
    
    if (token && !config._requestConfig?.skipAuthRefresh) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

axiosInstance.interceptors.response.use(
  (response: ApiAxiosResponse) => {
    const requestConfig = (response.config as ExtendedAxiosRequestConfig)._requestConfig
    
    if (requestConfig?.showSuccessToast) {
      showToast.success(requestConfig.successMessage || 'Operation successful')
    }
    
    return response
  },
  async (error: ApiAxiosError) => {
    const originalRequest = error.config as ExtendedAxiosRequestConfig
    const requestConfig = originalRequest._requestConfig
    
    if (error.response?.status === 401 && !originalRequest._retry && !requestConfig?.skipAuthRefresh) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`
          return axiosInstance(originalRequest)
        }).catch(err => {
          return Promise.reject(err)
        })
      }
      
      originalRequest._retry = true
      isRefreshing = true
      
      try {
        const state = store.getState()
        const refreshToken = state.auth.refreshToken
        
        if (!refreshToken) {
          throw new Error('No refresh token available')
        }
        
        const response = await axios.post(
          `${API_BASE_URL}${API_ENDPOINTS.auth.refresh}`,
          { refreshToken },
          { 
            headers: { 'Content-Type': 'application/json' },
            timeout: API_CONFIG.timeout
          }
        )
        
        const { token: newToken, refreshToken: newRefreshToken } = response.data
        
        if (newToken) {
          store.dispatch(updateToken({ token: newToken, refreshToken: newRefreshToken }))
          processQueue(null, newToken)
          originalRequest.headers.Authorization = `Bearer ${newToken}`
          return axiosInstance(originalRequest)
        }
      } catch (refreshError) {
        processQueue(refreshError as Error, null)
        store.dispatch(logout())
        window.location.href = '/login'
        return Promise.reject(refreshError)
      } finally {
        isRefreshing = false
      }
    }
    
    if (error.code === 'ERR_NETWORK' || error.code === 'ECONNABORTED') {
      const retryCount = originalRequest._retryCount || 0
      
      if (retryCount < API_CONFIG.retries && !requestConfig?.skipAuthRefresh) {
        originalRequest._retryCount = retryCount + 1
        
        const delay = API_CONFIG.retryDelay * Math.pow(2, retryCount)
        
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(axiosInstance(originalRequest))
          }, delay)
        })
      }
    }
    
    if (requestConfig?.showErrorToast !== false) {
      handleApiError(error)
    }
    
    return Promise.reject(error)
  }
)

export const apiClient = {
  get: <T = unknown>(url: string, config?: RequestConfig) => 
    axiosInstance.get<T>(url, { _requestConfig: config } as ExtendedAxiosRequestConfig),
    
  post: <T = unknown>(url: string, data?: unknown, config?: RequestConfig) => 
    axiosInstance.post<T>(url, data, { _requestConfig: config } as ExtendedAxiosRequestConfig),
    
  put: <T = unknown>(url: string, data?: unknown, config?: RequestConfig) => 
    axiosInstance.put<T>(url, data, { _requestConfig: config } as ExtendedAxiosRequestConfig),
    
  patch: <T = unknown>(url: string, data?: unknown, config?: RequestConfig) => 
    axiosInstance.patch<T>(url, data, { _requestConfig: config } as ExtendedAxiosRequestConfig),
    
  delete: <T = unknown>(url: string, config?: RequestConfig) => 
    axiosInstance.delete<T>(url, { _requestConfig: config } as ExtendedAxiosRequestConfig),
}

export default axiosInstance