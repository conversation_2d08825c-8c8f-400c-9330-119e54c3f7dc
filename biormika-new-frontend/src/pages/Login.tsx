import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Form } from 'antd'
import { AuthLayout } from '../components/AuthLayout'
import { AuthCard } from '../components/AuthCard'
import { BrandHeader } from '../components/BrandHeader'
import { FormField } from '../components/FormField'
import { PrimaryButton } from '../components/PrimaryButton'
import { SocialButton } from '../components/SocialButton'
import { useAuth } from '../hooks/useAuth'
import type { LoginCredentials } from '../types/auth'

export default function Login() {
  const [form] = Form.useForm()
  const { isLoading, isSubmitting, error, login, googleLogin, githubLogin, clearError } = useAuth()

  React.useEffect(() => {
    clearError()
    if (import.meta.env.VITE_ENVIRONMENT === 'development') {
      form.setFieldsValue({
        email: '<EMAIL>',
        password: 'password123'
      })
    }
  }, [clearError, form])

  const handleSubmit = async (values: LoginCredentials) => {
    await login(values)
  }

  return (
    <AuthLayout>
      <AuthCard>
        <BrandHeader />

        <div className="mb-6">
          <h2 className="text-xl font-semibold text-text-primary text-center">Login to your account</h2>
        </div>

        <Form form={form} layout="vertical" onFinish={handleSubmit} className="space-y-4">
          <FormField
            label="Email"
            name="email"
            type="email"
            placeholder="<EMAIL>"
            required
          />

          <FormField
            label="Password"
            name="password"
            type="password"
            placeholder="••••••••••"
            required
          />

          <div className="text-center mb-6">
            <Link
              to="/forgot-password"
              className="text-brand-primary hover:text-brand-primary-dark text-sm font-medium"
            >
              Forgot Password?
            </Link>
          </div>

          <Form.Item>
            <PrimaryButton htmlType="submit" loading={isLoading || isSubmitting}>
              Sign in
            </PrimaryButton>
          </Form.Item>

          {error && (
            <div className="text-error text-sm text-center mt-2">
              {error}
            </div>
          )}
        </Form>

        <div className="my-6 text-center">
          <span className="text-text-muted text-sm">Or</span>
        </div>

        <div className="space-y-3">
          <SocialButton 
            provider="google" 
            onClick={googleLogin}
            className={isLoading || isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
          >
            Sign in with Google
          </SocialButton>

          <SocialButton 
            provider="github" 
            onClick={githubLogin}
            className={isLoading || isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
          >
            Sign in with GitHub
          </SocialButton>
        </div>

        <div className="mt-8 text-center">
          <span className="text-text-secondary text-sm">
            Don't have an account?{' '}
            <Link to="/signup" className="text-brand-primary hover:text-brand-primary-dark font-medium">
              Sign up
            </Link>
          </span>
        </div>
      </AuthCard>
    </AuthLayout>
  )
}
