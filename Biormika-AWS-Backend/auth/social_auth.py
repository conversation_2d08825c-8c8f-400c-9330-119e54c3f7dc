import json
import os
import boto3
import requests

cognito = boto3.client('cognito-idp')
USER_POOL_ID = os.environ.get('COGNITO_USER_POOL_ID')
CLIENT_ID = os.environ.get('COGNITO_CLIENT_ID')


def create_response(status_code, body):
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': True,
        },
        'body': json.dumps(body)
    }


def google_auth(event, context):
    try:
        body = json.loads(event['body'])
        id_token = body.get('idToken')

        if not id_token:
            return create_response(400, {
                'success': False,
                'message': 'Google ID token is required',
                'statusCode': 400
            })

        google_response = requests.get(
            f'https://oauth2.googleapis.com/tokeninfo?id_token={id_token}'
        )

        if google_response.status_code != 200:
            return create_response(401, {
                'success': False,
                'message': 'Invalid Google token',
                'statusCode': 401
            })

        google_data = google_response.json()
        email = google_data.get('email')
        name = google_data.get('name')
        picture = google_data.get('picture')

        try:
            user_response = cognito.admin_get_user(
                UserPoolId=USER_POOL_ID,
                Username=email
            )

            user_attributes = {
                attr['Name']: attr['Value']
                for attr in user_response['UserAttributes']
            }

        except cognito.exceptions.UserNotFoundException:
            cognito.admin_create_user(
                UserPoolId=USER_POOL_ID,
                Username=email,
                UserAttributes=[
                    {'Name': 'email', 'Value': email},
                    {'Name': 'name', 'Value': name},
                    {'Name': 'email_verified', 'Value': 'true'},
                    {'Name': 'picture', 'Value': picture or ''}
                ],
                MessageAction='SUPPRESS'
            )

            user_attributes = {
                'sub': email,
                'email': email,
                'name': name,
                'picture': picture
            }

        from auth.handlers import generate_jwt_token

        user_data = {
            'id': user_attributes.get('sub'),
            'email': user_attributes.get('email'),
            'name': user_attributes.get('name'),
            'avatar': user_attributes.get('picture'),
            'role': user_attributes.get('custom:role', 'user'),
            'permissions': ['read', 'write']
        }

        token = generate_jwt_token(user_data)

        return create_response(200, {
            'success': True,
            'data': {
                'user': user_data,
                'token': token
            },
            'message': 'Google authentication successful',
            'statusCode': 200
        })

    except Exception as e:
        print(f"Google auth error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })


def facebook_auth(event, context):
    try:
        body = json.loads(event['body'])
        access_token = body.get('accessToken')

        if not access_token:
            return create_response(400, {
                'success': False,
                'message': 'Facebook access token is required',
                'statusCode': 400
            })

        fb_response = requests.get(
            f'https://graph.facebook.com/me?fields=id,name,email,picture'
            f'&access_token={access_token}'
        )

        if fb_response.status_code != 200:
            return create_response(401, {
                'success': False,
                'message': 'Invalid Facebook token',
                'statusCode': 401
            })

        fb_data = fb_response.json()
        email = fb_data.get('email')
        name = fb_data.get('name')
        picture = fb_data.get('picture', {}).get('data', {}).get('url')

        if not email:
            return create_response(400, {
                'success': False,
                'message': 'Email permission is required',
                'statusCode': 400
            })

        try:
            user_response = cognito.admin_get_user(
                UserPoolId=USER_POOL_ID,
                Username=email
            )

            user_attributes = {attr['Name']: attr['Value']
                               for attr in user_response['UserAttributes']}

        except cognito.exceptions.UserNotFoundException:
            cognito.admin_create_user(
                UserPoolId=USER_POOL_ID,
                Username=email,
                UserAttributes=[
                    {'Name': 'email', 'Value': email},
                    {'Name': 'name', 'Value': name},
                    {'Name': 'email_verified', 'Value': 'true'},
                    {'Name': 'picture', 'Value': picture or ''}
                ],
                MessageAction='SUPPRESS'
            )

            user_attributes = {
                'sub': email,
                'email': email,
                'name': name,
                'picture': picture
            }

        from auth.handlers import generate_jwt_token

        user_data = {
            'id': user_attributes.get('sub'),
            'email': user_attributes.get('email'),
            'name': user_attributes.get('name'),
            'avatar': user_attributes.get('picture'),
            'role': user_attributes.get('custom:role', 'user'),
            'permissions': ['read', 'write']
        }

        token = generate_jwt_token(user_data)

        return create_response(200, {
            'success': True,
            'data': {
                'user': user_data,
                'token': token
            },
            'message': 'Facebook authentication successful',
            'statusCode': 200
        })

    except Exception as e:
        print(f"Facebook auth error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })
