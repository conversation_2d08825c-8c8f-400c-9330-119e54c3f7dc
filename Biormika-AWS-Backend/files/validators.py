import os
import boto3

s3 = boto3.client('s3')
S3_BUCKET = os.environ.get('S3_BUCKET')


def validate_edf_file(s3_key):
    try:
        response = s3.get_object(Bucket=S3_BUCKET, Key=s3_key)
        file_content = response['Body'].read()

        if len(file_content) < 256:
            return {
                'valid': False,
                'error': 'File too small to be a valid EDF file'
            }

        version = file_content[0:8].decode('ascii', errors='ignore').strip()
        if version != '0':
            return {
                'valid': False,
                'error': f'Invalid EDF version: {version}'
            }

        try:
            header_bytes = int(file_content[184:192].decode('ascii').strip())
            num_signals = int(file_content[252:256].decode('ascii').strip())

            if header_bytes < 256:
                return {
                    'valid': False,
                    'error': 'Invalid header size'
                }

            if num_signals < 1:
                return {
                    'valid': False,
                    'error': 'No signals found in EDF file'
                }

            label_start = 256
            label_end = label_start + (16 * num_signals)

            if len(file_content) < label_end:
                return {
                    'valid': False,
                    'error': 'File truncated - cannot read channel labels'
                }

            channels = []
            for i in range(num_signals):
                start = label_start + (i * 16)
                end = start + 16
                label = file_content[start:end].decode(
                    'ascii', errors='ignore').strip()
                if label:
                    channels.append(label)

            samples_start = 256 + (num_signals * 216)
            samples_end = samples_start + (num_signals * 8)

            if len(file_content) < samples_end:
                return {
                    'valid': False,
                    'error': 'File truncated - cannot read sampling information'
                }

            sampling_rates = []
            for i in range(num_signals):
                start = samples_start + (i * 8)
                end = start + 8
                samples_per_record = int(
                    file_content[start:end].decode(
                        'ascii', errors='ignore').strip()
                )
                sampling_rates.append(samples_per_record)

            duration_per_record = float(
                file_content[244:252].decode('ascii').strip())
            num_records = int(file_content[236:244].decode('ascii').strip())
            total_duration = duration_per_record * num_records

            return {
                'valid': True,
                'channels': channels,
                'numChannels': num_signals,
                'samplingRate': max(sampling_rates) if sampling_rates else 0,
                'duration': total_duration,
                'numRecords': num_records,
                'headerSize': header_bytes
            }

        except (ValueError, UnicodeDecodeError) as e:
            return {
                'valid': False,
                'error': f'Error parsing EDF header: {str(e)}'
            }

    except Exception as e:
        print(f"EDF validation error: {e}")
        return {
            'valid': False,
            'error': f'Failed to validate EDF file: {str(e)}'
        }


def validate_file_size(file_size):
    max_size = 104857600  # 100MB

    if file_size > max_size:
        return {
            'valid': False,
            'error': f'File size exceeds maximum allowed size of '
            f'{max_size / (1024 * 1024)}MB'
        }

    if file_size < 256:
        return {
            'valid': False,
            'error': 'File too small to be a valid EDF file'
        }

    return {'valid': True}


def validate_file_extension(filename):
    if not filename.lower().endswith('.edf'):
        return {
            'valid': False,
            'error': 'Only EDF files are supported'
        }

    return {'valid': True}
