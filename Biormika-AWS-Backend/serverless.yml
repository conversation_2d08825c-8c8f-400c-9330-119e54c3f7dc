service: biormika-hfo-backend

frameworkVersion: '3'

provider:
  name: aws
  runtime: python3.9
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  environment:
    STAGE: ${self:provider.stage}
    REGION: ${self:provider.region}
    S3_BUCKET: ${self:custom.s3Bucket}
    COGNITO_USER_POOL_ID: 
      Ref: CognitoUserPool
    COGNITO_CLIENT_ID:
      Ref: CognitoUserPoolClient
    DYNAMODB_TABLE: ${self:service}-${self:provider.stage}
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - s3:GetObject
            - s3:PutObject
            - s3:DeleteObject
          Resource:
            - arn:aws:s3:::${self:custom.s3Bucket}/*
        - Effect: Allow
          Action:
            - s3:CreateBucket
            - s3:ListBucket
          Resource:
            - arn:aws:s3:::${self:custom.s3Bucket}
        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:Scan
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
          Resource:
            - "Fn::GetAtt": [DynamoDBTable, Arn]
        - Effect: Allow
          Action:
            - cognito-idp:AdminInitiateAuth
            - cognito-idp:AdminCreateUser
            - cognito-idp:AdminSetUserPassword
            - cognito-idp:AdminGetUser
          Resource:
            - "Fn::GetAtt": [CognitoUserPool, Arn]
        - Effect: Allow
          Action:
            - states:StartExecution
          Resource:
            - Ref: AnalysisStateMachine

custom:
  s3Bucket: ${self:service}-${self:provider.stage}-files
  pythonRequirements:
    dockerizePip: true
    layer: true
    slim: true
    strip: false
    noDeploy:
      - boto3
      - botocore
  serverless-offline:
    httpPort: 3001
    websocketPort: 3003
  dotenv:
    path: .env.${self:provider.stage}

plugins:
  - serverless-python-requirements
  - serverless-offline
  - serverless-dotenv-plugin

layers:
  pythonRequirements:
    path: requirements.txt
    compatibleRuntimes:
      - python3.9

functions:
  # Authentication Functions
  login:
    handler: auth/handlers.login
    events:
      - http:
          path: auth/login
          method: post
          cors: true

  signup:
    handler: auth/handlers.signup
    events:
      - http:
          path: auth/signup
          method: post
          cors: true

  refresh:
    handler: auth/handlers.refresh_token
    events:
      - http:
          path: auth/refresh
          method: post
          cors: true

  # File Management Functions
  initUpload:
    handler: files/handlers.init_upload
    events:
      - http:
          path: files/upload/init
          method: post
          cors: true
          authorizer:
            name: authorizer
            type: COGNITO_USER_POOLS
            arn:
              Fn::GetAtt:
                - CognitoUserPool
                - Arn

  validateFile:
    handler: files/handlers.validate_file
    events:
      - http:
          path: files/validate/{fileId}
          method: post
          cors: true
          authorizer:
            name: authorizer
            type: COGNITO_USER_POOLS
            arn:
              Fn::GetAtt:
                - CognitoUserPool
                - Arn

  # Analysis Functions
  startAnalysis:
    handler: analysis/handlers.start_analysis
    timeout: 30
    events:
      - http:
          path: analysis/start
          method: post
          cors: true
          authorizer:
            name: authorizer
            type: COGNITO_USER_POOLS
            arn:
              Fn::GetAtt:
                - CognitoUserPool
                - Arn

  getAnalysisStatus:
    handler: analysis/handlers.get_status
    events:
      - http:
          path: analysis/status/{analysisId}
          method: get
          cors: true
          authorizer:
            name: authorizer
            type: COGNITO_USER_POOLS
            arn:
              Fn::GetAtt:
                - CognitoUserPool
                - Arn

  getAnalysisResults:
    handler: analysis/handlers.get_results
    events:
      - http:
          path: analysis/results/{analysisId}
          method: get
          cors: true
          authorizer:
            name: authorizer
            type: COGNITO_USER_POOLS
            arn:
              Fn::GetAtt:
                - CognitoUserPool
                - Arn

  # Step Function Tasks
  readEdfFile:
    handler: analysis/tasks.read_edf_file
    timeout: 300
    memorySize: 3008
    layers:
      - Ref: PythonRequirementsLambdaLayer

  detectHfo:
    handler: analysis/tasks.detect_hfo
    timeout: 900
    memorySize: 3008
    layers:
      - Ref: PythonRequirementsLambdaLayer

  generateVisualization:
    handler: analysis/tasks.generate_visualization
    timeout: 300
    memorySize: 3008
    layers:
      - Ref: PythonRequirementsLambdaLayer

  processResults:
    handler: analysis/tasks.process_results
    timeout: 60
    layers:
      - Ref: PythonRequirementsLambdaLayer

  # WebSocket Functions
  websocketConnect:
    handler: websocket/handlers.connect
    events:
      - websocket:
          route: $connect

  websocketDisconnect:
    handler: websocket/handlers.disconnect
    events:
      - websocket:
          route: $disconnect

  websocketDefault:
    handler: websocket/handlers.default
    events:
      - websocket:
          route: $default

resources:
  Resources:
    # S3 Bucket
    S3Bucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:custom.s3Bucket}
        CorsConfiguration:
          CorsRules:
            - AllowedHeaders: ['*']
              AllowedMethods: [GET, PUT, POST, DELETE, HEAD]
              AllowedOrigins: ['*']
              MaxAge: 3000

    # DynamoDB Tables
    DynamoDBTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:service}-${self:provider.stage}
        AttributeDefinitions:
          - AttributeName: PK
            AttributeType: S
          - AttributeName: SK
            AttributeType: S
          - AttributeName: GSI1PK
            AttributeType: S
          - AttributeName: GSI1SK
            AttributeType: S
        KeySchema:
          - AttributeName: PK
            KeyType: HASH
          - AttributeName: SK
            KeyType: RANGE
        GlobalSecondaryIndexes:
          - IndexName: GSI1
            KeySchema:
              - AttributeName: GSI1PK
                KeyType: HASH
              - AttributeName: GSI1SK
                KeyType: RANGE
            Projection:
              ProjectionType: ALL
            ProvisionedThroughput:
              ReadCapacityUnits: 5
              WriteCapacityUnits: 5
        BillingMode: PROVISIONED
        ProvisionedThroughput:
          ReadCapacityUnits: 5
          WriteCapacityUnits: 5

    # Cognito User Pool
    CognitoUserPool:
      Type: AWS::Cognito::UserPool
      Properties:
        UserPoolName: ${self:service}-${self:provider.stage}-user-pool
        Schema:
          - Name: email
            AttributeDataType: String
            Mutable: true
            Required: true
          - Name: name
            AttributeDataType: String
            Mutable: true
            Required: true
        Policies:
          PasswordPolicy:
            MinimumLength: 8
            RequireUppercase: true
            RequireLowercase: true
            RequireNumbers: true
            RequireSymbols: false
        AutoVerifiedAttributes:
          - email
        UsernameAttributes:
          - email
        MfaConfiguration: OFF

    CognitoUserPoolClient:
      Type: AWS::Cognito::UserPoolClient
      Properties:
        ClientName: ${self:service}-${self:provider.stage}-client
        GenerateSecret: false
        UserPoolId:
          Ref: CognitoUserPool
        ExplicitAuthFlows:
          - ALLOW_USER_PASSWORD_AUTH
          - ALLOW_REFRESH_TOKEN_AUTH
        SupportedIdentityProviders:
          - COGNITO
        AllowedOAuthFlows:
          - code
          - implicit
        AllowedOAuthScopes:
          - email
          - openid
          - profile
        CallbackURLs:
          - http://localhost:3000/callback
          - https://app.biormika.com/callback

    # Step Functions State Machine
    AnalysisStateMachine:
      Type: AWS::StepFunctions::StateMachine
      Properties:
        StateMachineName: ${self:service}-${self:provider.stage}-analysis
        RoleArn:
          Fn::GetAtt: [StatesExecutionRole, Arn]
        DefinitionString:
          Fn::Sub: |
            {
              "Comment": "HFO Analysis Workflow",
              "StartAt": "ReadEdfFile",
              "States": {
                "ReadEdfFile": {
                  "Type": "Task",
                  "Resource": "${ReadEdfFileLambdaFunction.Arn}",
                  "Next": "DetectHfo",
                  "Retry": [
                    {
                      "ErrorEquals": ["States.TaskFailed"],
                      "IntervalSeconds": 2,
                      "MaxAttempts": 3,
                      "BackoffRate": 2
                    }
                  ]
                },
                "DetectHfo": {
                  "Type": "Task",
                  "Resource": "${DetectHfoLambdaFunction.Arn}",
                  "Next": "GenerateVisualization",
                  "Retry": [
                    {
                      "ErrorEquals": ["States.TaskFailed"],
                      "IntervalSeconds": 2,
                      "MaxAttempts": 3,
                      "BackoffRate": 2
                    }
                  ]
                },
                "GenerateVisualization": {
                  "Type": "Task",
                  "Resource": "${GenerateVisualizationLambdaFunction.Arn}",
                  "Next": "ProcessResults",
                  "Retry": [
                    {
                      "ErrorEquals": ["States.TaskFailed"],
                      "IntervalSeconds": 2,
                      "MaxAttempts": 3,
                      "BackoffRate": 2
                    }
                  ]
                },
                "ProcessResults": {
                  "Type": "Task",
                  "Resource": "${ProcessResultsLambdaFunction.Arn}",
                  "End": true
                }
              }
            }

    StatesExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service:
                  - states.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: StatesExecutionPolicy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - lambda:InvokeFunction
                  Resource:
                    - Fn::GetAtt: [ReadEdfFileLambdaFunction, Arn]
                    - Fn::GetAtt: [DetectHfoLambdaFunction, Arn]
                    - Fn::GetAtt: [GenerateVisualizationLambdaFunction, Arn]
                    - Fn::GetAtt: [ProcessResultsLambdaFunction, Arn]

  Outputs:
    UserPoolId:
      Value:
        Ref: CognitoUserPool
    UserPoolClientId:
      Value:
        Ref: CognitoUserPoolClient
    S3BucketName:
      Value:
        Ref: S3Bucket
    DynamoDBTableName:
      Value:
        Ref: DynamoDBTable
    StateMachineArn:
      Value:
        Ref: AnalysisStateMachine